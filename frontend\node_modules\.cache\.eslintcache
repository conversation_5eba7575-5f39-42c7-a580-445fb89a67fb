[{"D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\index.js": "1", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\App.js": "2", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\toolbar.js": "3", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\ui.js": "4", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\submit.js": "5", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\draggableNode.js": "6", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\store.js": "7", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\inputNode.js": "8", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\outputNode.js": "9", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\textNode.js": "10", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\llmNode.js": "11", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\BaseNode.js": "12", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\switchNode.js": "13", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\mathNode.js": "14", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\filterNode.js": "15", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\aggregatorNode.js": "16", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\timerNode.js": "17", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\styles\\theme.js": "18", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\components\\NodeInput.js": "19", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\components\\Modal.js": "20"}, {"size": 254, "mtime": 1750274986338, "results": "21", "hashOfConfig": "22"}, {"size": 1071, "mtime": 1750317453973, "results": "23", "hashOfConfig": "22"}, {"size": 1960, "mtime": 1750283589421, "results": "24", "hashOfConfig": "22"}, {"size": 6046, "mtime": 1750351772547, "results": "25", "hashOfConfig": "22"}, {"size": 4136, "mtime": 1750314956052, "results": "26", "hashOfConfig": "22"}, {"size": 621, "mtime": 1750285171720, "results": "27", "hashOfConfig": "22"}, {"size": 1353, "mtime": 1750285195771, "results": "28", "hashOfConfig": "22"}, {"size": 1440, "mtime": 1750315054040, "results": "29", "hashOfConfig": "22"}, {"size": 1453, "mtime": 1750315064718, "results": "30", "hashOfConfig": "22"}, {"size": 4636, "mtime": 1750349986318, "results": "31", "hashOfConfig": "22"}, {"size": 762, "mtime": 1750315031300, "results": "32", "hashOfConfig": "22"}, {"size": 4244, "mtime": 1750350043099, "results": "33", "hashOfConfig": "22"}, {"size": 1357, "mtime": 1750315019076, "results": "34", "hashOfConfig": "22"}, {"size": 1367, "mtime": 1750314982036, "results": "35", "hashOfConfig": "22"}, {"size": 1564, "mtime": 1750315007000, "results": "36", "hashOfConfig": "22"}, {"size": 2110, "mtime": 1750314994727, "results": "37", "hashOfConfig": "22"}, {"size": 1396, "mtime": 1750315076112, "results": "38", "hashOfConfig": "22"}, {"size": 2138, "mtime": 1750279121739, "results": "39", "hashOfConfig": "22"}, {"size": 718, "mtime": 1750286504985, "results": "40", "hashOfConfig": "22"}, {"size": 3508, "mtime": 1750314892016, "results": "41", "hashOfConfig": "22"}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "45"}, "159wf7k", {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "45"}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "45"}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "45"}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "45"}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "45"}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\index.js", [], [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\App.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\toolbar.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\ui.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\submit.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\draggableNode.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\store.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\inputNode.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\outputNode.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\textNode.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\llmNode.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\BaseNode.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\switchNode.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\mathNode.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\filterNode.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\aggregatorNode.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\timerNode.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\styles\\theme.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\components\\NodeInput.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\components\\Modal.js", [], []]