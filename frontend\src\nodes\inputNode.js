// inputNode.js

import { useState } from 'react';
import { BaseNode, HANDLE_CONFIGS } from './BaseNode';

export const InputNode = ({ id, data }) => {
  const [selectedFile, setSelectedFile] = useState(null);

  const handleFileUpload = (e) => {
    const file = e.target.files[0];
    setSelectedFile(file);
  };

  const handles = [HANDLE_CONFIGS.sourceRight(`${id}-value`)];

  return (
    <BaseNode
      id={id}
      data={data}
      title="Input"
      handles={handles}
      nodeType="input"
      height={120}
    >
      <div className="input-node-content">
        {/* File Section */}
        <div className="input-section">
          <div className="input-section-header">
            File
          </div>
        </div>

        {/* Upload File Section */}
        <div className="input-section upload-section">
          <label className="upload-label">
            <input
              type="file"
              onChange={handleFileUpload}
              style={{ display: 'none' }}
              accept="*/*"
            />
            <div className="upload-button">
              Upload file
            </div>
          </label>
          {selectedFile && (
            <div className="file-info">
              {selectedFile.name}
            </div>
          )}
        </div>
      </div>
    </BaseNode>
  );
}
